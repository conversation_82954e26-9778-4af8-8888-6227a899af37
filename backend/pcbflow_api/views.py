import os
import csv
import json
import zipfile
from io import StringIO
from decimal import Decimal

from django.shortcuts import get_object_or_404
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import transaction
from django.db.models import Q, Sum, F
from django.http import HttpResponse
from django.views.decorators.clickjacking import xframe_options_exempt
from django.utils.decorators import method_decorator

from rest_framework import status, viewsets, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON>PartPars<PERSON>, FormParser, JSONParser
from django.db import transaction

from utils.permissions import IsSupervisor

from .models import Component, Vendor, ComponentVendor, Board, BoardBOMItem, InventoryTransaction
from .serializers import (
    ComponentSerializer, ComponentListSerializer, ComponentStockSerializer,
    VendorSerializer, ComponentVendorSerializer, BoardSerializer, BoardListSerializer,
    BoardBOMItemSerializer, InventoryTransactionSerializer, BoardCalculationSerializer,
    ComponentShortageSerializer, VendorInfoSerializer
)


class ComponentViewSet(viewsets.ModelViewSet):
    """ViewSet for Component CRUD operations"""
    queryset = Component.objects.all()
    permission_classes = [IsSupervisor]
    lookup_field = 'id'

    def get_serializer_class(self):
        if self.action == 'list':
            return ComponentListSerializer
        elif self.action in ['adjust_stock']:
            return ComponentStockSerializer
        return ComponentSerializer

    def get_queryset(self):
        queryset = Component.objects.all()

        # Filter by low stock
        low_stock = self.request.query_params.get('low_stock', None)
        if low_stock == 'true':
            queryset = queryset.filter(stock_quantity__lte=F('min_stock'))

        # Search by part number or name
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(part_number__icontains=search) |
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('part_number')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def adjust_stock(self, request, id=None):
        """Adjust component stock quantity"""
        component = self.get_object()
        adjustment = request.data.get('adjustment', 0)
        ref_link = request.data.get('ref_link', '')
        note = request.data.get('note', '')

        try:
            adjustment = int(adjustment)
        except (ValueError, TypeError):
            return Response({'error': 'Invalid adjustment value'}, status=status.HTTP_400_BAD_REQUEST)

        if adjustment == 0:
            return Response({'error': 'Adjustment cannot be zero'}, status=status.HTTP_400_BAD_REQUEST)

        # Create inventory transaction
        transaction_type = 'adjustment'
        InventoryTransaction.objects.create(
            component=component,
            quantity=adjustment,
            transaction_type=transaction_type,
            ref_link=ref_link,
            note=note,
            created_by=request.user
        )
        # Update component stock quantity
        component.stock_quantity += adjustment
        component.save()

        return Response({
            'message': 'Stock adjusted successfully',
            'new_stock': component.stock_quantity
        })

    @action(detail=False, methods=['post'])
    def bulk_stock_adjustment(self, request):
        """Bulk stock adjustment from Excel import"""
        records = request.data.get('records', [])

        if not records:
            return Response({'error': 'No records provided'}, status=status.HTTP_400_BAD_REQUEST)

        results = {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'errors': []
        }

        with transaction.atomic():
            for i, record in enumerate(records):
                try:
                    part_number = record.get('part_number')
                    component_name = record.get('component_name')
                    adjustment_qty = record.get('adjustment_qty', 0)
                    is_stock_in = record.get('is_stock_in', True)
                    note = record.get('note', '')

                    if not part_number or not component_name:
                        results['errors'].append({
                            'record': i + 1,
                            'error': 'Part number and component name are required'
                        })
                        continue

                    # Get or create component
                    component, created = Component.objects.get_or_create(
                        part_number=part_number,
                        defaults={
                            'name': component_name,
                            'package': record.get('package', ''),
                            'footprint': record.get('footprint', ''),
                            'description': record.get('description', ''),
                            'stock_quantity': 0,
                            'min_stock': record.get('min_stock', 0),
                            'created_by': request.user
                        }
                    )

                    if created:
                        results['created'] += 1
                    else:
                        # Update existing component info if provided
                        if record.get('min_stock') is not None:
                            component.min_stock = record.get('min_stock')
                        if record.get('description'):
                            component.description = record.get('description')
                        component.save()
                        results['updated'] += 1

                    # Process stock adjustment
                    if adjustment_qty != 0:
                        try:
                            adjustment_qty = int(adjustment_qty)
                        except (ValueError, TypeError):
                            results['errors'].append({
                                'record': i + 1,
                                'part_number': part_number,
                                'error': 'Invalid adjustment quantity'
                            })
                            continue

                        # Calculate transaction based on stock in/out
                        if is_stock_in:
                            # Nhập kho: positive quantity
                            transaction_quantity = adjustment_qty
                            transaction_type = 'in'
                        else:
                            # Xuất kho: negative quantity
                            transaction_quantity = -adjustment_qty
                            transaction_type = 'out'

                            # Check if we have enough stock for stock_out
                            if component.stock_quantity < adjustment_qty:
                                results['errors'].append({
                                    'record': i + 1,
                                    'part_number': part_number,
                                    'error': f'Insufficient stock. Current: {component.stock_quantity}, Requested: {adjustment_qty}'
                                })
                                continue

                        # Create inventory transaction (model save method will update stock_quantity automatically)
                        InventoryTransaction.objects.create(
                            component=component,
                            quantity=transaction_quantity,
                            transaction_type=transaction_type,
                            note=note,
                            created_by=request.user
                        )
                        component.stock_quantity += transaction_quantity
                        component.save()

                    # Handle vendor information if provided
                    vendor_name = record.get('vendor_name')
                    if vendor_name and record.get('price'):
                        vendor, _ = Vendor.objects.get_or_create(
                            name=vendor_name,
                            defaults={'website': '', 'contact_info': ''}
                        )

                        ComponentVendor.objects.update_or_create(
                            component=component,
                            vendor=vendor,
                            defaults={
                                'price': record.get('price'),
                                'currency': record.get('currency', 'VND'),
                                'link': record.get('link', ''),
                                'is_preferred': False  # Don't auto-set as preferred
                            }
                        )

                    results['processed'] += 1

                except Exception as e:
                    results['errors'].append({
                        'record': i + 1,
                        'part_number': record.get('part_number', 'Unknown'),
                        'error': str(e)
                    })

        return Response(results)

    @action(detail=True, methods=['get'])
    def vendors(self, request, id=None):
        """Get all vendors for a component"""
        component = self.get_object()
        vendors = component.vendors.all().order_by('price')
        serializer = ComponentVendorSerializer(vendors, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_vendor(self, request, id=None):
        """Add vendor information for a component"""
        component = self.get_object()
        serializer = VendorInfoSerializer(data=request.data)

        if serializer.is_valid():
            data = serializer.validated_data

            # Get or create vendor
            vendor, created = Vendor.objects.get_or_create(
                name=data['vendor_name'],
                defaults={
                    'website': data.get('vendor_website', ''),
                    'contact_info': data.get('vendor_contact_info', '')
                }
            )

            # Create or update component vendor relationship
            component_vendor, created = ComponentVendor.objects.update_or_create(
                component=component,
                vendor=vendor,
                defaults={
                    'price': data['price'],
                    'currency': data['currency'],
                    'link': data.get('link', ''),
                    'is_preferred': data['is_preferred'],
                    'moq': data.get('moq', 1)
                }
            )

            return Response(ComponentVendorSerializer(component_vendor).data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ComponentVendorViewSet(viewsets.ModelViewSet):
    """ViewSet for ComponentVendor CRUD operations"""
    queryset = ComponentVendor.objects.all()
    serializer_class = ComponentVendorSerializer
    permission_classes = [IsSupervisor]

    def get_queryset(self):
        queryset = ComponentVendor.objects.all()

        # Filter by component
        component_id = self.request.query_params.get('component_id', None)
        if component_id:
            queryset = queryset.filter(component__id=component_id)

        return queryset.order_by('price')

    @action(detail=True, methods=['patch'])
    def toggle_active(self, request, pk=None):
        """Toggle active status of a component vendor"""
        component_vendor = self.get_object()
        if component_vendor.vendor.active == False:
            return Response({'detail': f'Vendor {component_vendor.vendor.name} is not active. Please activate the vendor first!'}, 
                            status=status.HTTP_400_BAD_REQUEST)
        component_vendor.active = not component_vendor.active

        # If component vendor is deactivated, set is_preferred to False
        if not component_vendor.active:
            component_vendor.is_preferred = False

        component_vendor.save()

        serializer = self.get_serializer(component_vendor)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def set_preferred(self, request, pk=None):
        """Set this vendor as preferred for the component"""
        component_vendor = self.get_object()

        # Unset all other preferred vendors for this component
        ComponentVendor.objects.filter(
            component=component_vendor.component,
            is_preferred=True
        ).update(is_preferred=False)

        # Set this vendor as preferred
        component_vendor.is_preferred = True
        component_vendor.save()

        return Response({
            'message': 'Preferred vendor updated successfully',
            'data': ComponentVendorSerializer(component_vendor).data
        })


class VendorViewSet(viewsets.ModelViewSet):
    """ViewSet for Vendor CRUD operations"""
    queryset = Vendor.objects.all()
    serializer_class = VendorSerializer
    permission_classes = [IsSupervisor]

    def get_queryset(self):
        queryset = Vendor.objects.all()

        # Search by name
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(name__icontains=search)

        # Filter by active status
        active_only = self.request.query_params.get('active_only', None)
        if active_only and active_only.lower() == 'true':
            queryset = queryset.filter(active=True)

        return queryset.order_by('name')

    @action(detail=True, methods=['get'])
    def components(self, request, pk=None):
        """Get all components for a vendor"""
        vendor = self.get_object()
        component_vendors = vendor.components.all().order_by('component__part_number')
        serializer = ComponentVendorSerializer(component_vendors, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def toggle_active(self, request, pk=None):
        """Toggle active status of a vendor"""
        vendor = self.get_object()
        vendor.active = not vendor.active
        vendor.save()

        # If vendor is deactivated, set all its component vendors as non-preferred
        if not vendor.active:
            ComponentVendor.objects.filter(vendor=vendor, is_preferred=True).update(is_preferred=False)
            ComponentVendor.objects.filter(vendor=vendor, active=True).update(active=False)

        # If vendor is activated, set all its component vendors as active
        else:
            ComponentVendor.objects.filter(vendor=vendor, active=False).update(active=True)

        serializer = self.get_serializer(vendor)
        return Response(serializer.data)


class BoardViewSet(viewsets.ModelViewSet):
    """ViewSet for Board CRUD operations"""
    queryset = Board.objects.all()
    permission_classes = [IsSupervisor]
    parser_classes = [MultiPartParser, FormParser, JSONParser]

    def get_serializer_class(self):
        if self.action == 'list':
            return BoardListSerializer
        return BoardSerializer

    def get_queryset(self):
        queryset = Board.objects.all()

        # Search by name
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(uploaded_by=self.request.user)

    def update(self, request, *args, **kwargs):
        """Update board with optional file upload"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Handle file upload if provided
        if 'file' in request.FILES:
            zip_file = request.FILES['file']

            if not zip_file.name.endswith('.zip'):
                return Response({'error': 'File must be a ZIP archive'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # Validate ZIP file
                try:
                    with zipfile.ZipFile(zip_file, 'r') as test_zip:
                        test_zip.testzip()
                except zipfile.BadZipFile:
                    return Response({'error': 'Invalid ZIP file'}, status=status.HTTP_400_BAD_REQUEST)

                # Reset file pointer after validation
                zip_file.seek(0)

                # Process ZIP file to update board files
                self._process_zip_file(zip_file, instance)
            except Exception as e:
                return Response({'error': f'Error processing ZIP file: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        # Update other fields
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def upload_zip(self, request):
        """Upload and process ZIP file containing board files"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)

        zip_file = request.FILES['file']
        board_name = request.data.get('name', os.path.splitext(zip_file.name)[0])
        board_description = request.data.get('description', '')
        board_version = request.data.get('version', '')

        if not zip_file.name.endswith('.zip'):
            return Response({'error': 'File must be a ZIP archive'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Validate ZIP file
            try:
                with zipfile.ZipFile(zip_file, 'r') as test_zip:
                    test_zip.testzip()
            except zipfile.BadZipFile:
                return Response({'error': 'Invalid ZIP file'}, status=status.HTTP_400_BAD_REQUEST)

            # Reset file pointer after validation
            zip_file.seek(0)

            with transaction.atomic():
                # Create board instance
                board = Board.objects.create(
                    name=board_name,
                    description=board_description,
                    version=board_version,
                    original_filename=zip_file.name,
                    uploaded_by=request.user
                )

                # Process ZIP file
                self._process_zip_file(zip_file, board)

                serializer = BoardSerializer(board)
                return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            import traceback
            print(f"ZIP upload error: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def _process_zip_file(self, zip_file, board):
        """Process uploaded ZIP file and extract relevant files"""
        try:
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                file_list = zip_ref.namelist()

                # Create board directory
                board_dir = f'pcbflow/boards/{board.id}'
                print(f"Board directory: {board_dir}")

                # Process each file in the ZIP
                for file_path in file_list:
                    if file_path.endswith('/'):  # Skip directories
                        continue

                    # Skip macOS metadata files and folders
                    if file_path.startswith('__MACOSX/') or file_path.startswith('._') or '/__MACOSX/' in file_path:
                        continue

                    # Skip hidden files and system files
                    if os.path.basename(file_path).startswith('.'):
                        continue

                    filename = os.path.basename(file_path).lower()
                    print(f"Processing file: {file_path} -> {filename}")

                    # Extract and save PNG files as avatar
                    if filename.endswith('.png') and not board.avatar:
                        try:
                            print(f"Processing PNG file: {file_path}")
                            file_content = zip_ref.read(file_path)
                            print(f"PNG file size: {len(file_content)} bytes")
                            board.avatar.save(
                                filename,
                                ContentFile(file_content),
                                save=False
                            )
                            print(f"PNG file saved successfully: {filename}")
                        except Exception as e:
                            print(f"Error processing PNG file {file_path}: {str(e)}")

                    # Extract and save BOM CSV file
                    elif filename == 'bom.csv' or filename.endswith('.csv'):
                        try:
                            print(f"Processing BOM CSV file: {file_path}")
                            file_content = zip_ref.read(file_path)
                            print(f"BOM CSV file size: {len(file_content)} bytes")
                            board.bom_file.save(
                                'bom.csv',
                                ContentFile(file_content),
                                save=False
                            )
                            print(f"BOM CSV file saved successfully")

                            # Parse BOM file - try different encodings
                            try:
                                # Try UTF-8 first
                                bom_text = file_content.decode('utf-8')
                                print("BOM decoded with UTF-8")
                            except UnicodeDecodeError:
                                try:
                                    # Try UTF-8 with BOM
                                    bom_text = file_content.decode('utf-8-sig')
                                    print("BOM decoded with UTF-8-sig")
                                except UnicodeDecodeError:
                                    try:
                                        # Try Windows-1252 (common for Excel exports)
                                        bom_text = file_content.decode('windows-1252')
                                        print("BOM decoded with Windows-1252")
                                    except UnicodeDecodeError:
                                        try:
                                            # Try ISO-8859-1 (Latin-1)
                                            bom_text = file_content.decode('iso-8859-1')
                                            print("BOM decoded with ISO-8859-1")
                                        except UnicodeDecodeError:
                                            # Last resort - ignore errors
                                            bom_text = file_content.decode('utf-8', errors='ignore')
                                            print("BOM decoded with UTF-8 (ignoring errors)")

                            self._parse_bom_file(bom_text, board)
                            print("BOM parsing completed")
                        except Exception as e:
                            print(f"Error processing BOM CSV file {file_path}: {str(e)}")

                    # Extract and save iBOM HTML file
                    elif filename == 'ibom.html' or 'ibom' in filename and filename.endswith('.html'):
                        try:
                            print(f"Processing iBOM HTML file: {file_path}")
                            file_content = zip_ref.read(file_path)
                            print(f"iBOM HTML file size: {len(file_content)} bytes")
                            board.ibom_file.save(
                                'ibom.html',
                                ContentFile(file_content),
                                save=False
                            )
                            print(f"iBOM HTML file saved successfully")
                        except Exception as e:
                            print(f"Error processing iBOM HTML file {file_path}: {str(e)}")

                    # Extract Gerber files
                    elif any(filename.endswith(ext) for ext in ['.gbr', '.gbl', '.gtl', '.gts', '.gbs', '.gto', '.gbo', '.drl', '.txt']):
                        file_content = zip_ref.read(file_path)
                        gerber_path = f'{board_dir}/gerber/{filename}'
                        default_storage.save(gerber_path, ContentFile(file_content))

                        if not board.gerber_path:
                            board.gerber_path = f'{board_dir}/gerber/'

                print(f"ZIP file processing completed successfully")
            board.save()
            print(f"Board saved successfully: {board.id}")
        except Exception as e:
            print(f"Error in _process_zip_file: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def _parse_bom_file(self, csv_content, board):
        """Parse BOM CSV file and create BoardBOMItem instances"""
        try:
            # Clean up the CSV content
            csv_content = csv_content.strip()
            if not csv_content:
                return

            # Try different CSV dialects
            try:
                csv_reader = csv.DictReader(StringIO(csv_content))
            except Exception:
                # Try with semicolon delimiter (common in European Excel exports)
                csv_reader = csv.DictReader(StringIO(csv_content), delimiter=';')

            for row_num, row in enumerate(csv_reader, 1):
                try:
                    # Skip empty rows
                    if not any(row.values()):
                        continue

                    # Try to match CSV column names (case insensitive)
                    row_lower = {k.lower().strip(): v.strip() if v else '' for k, v in row.items()}

                    # Map to your specific CSV headers: Sourced, Placed, References, Value, Footprint, Quantity
                    # Extract values from CSV columns
                    value = row_lower.get('value', '')
                    footprint = row_lower.get('footprint', '')
                    quantity = row_lower.get('quantity', '')
                    references = row_lower.get('references', '')

                    # Create part_number from Value + Footprint combination
                    # This creates a unique identifier for each component type
                    if value and footprint:
                        part_number = f"{value}_{footprint}".replace(' ', '_').replace('/', '_')
                    elif value:
                        part_number = value.replace(' ', '_').replace('/', '_')
                    elif footprint:
                        part_number = footprint.replace(' ', '_').replace('/', '_')
                    else:
                        part_number = 'UNKNOWN'

                    # Use value as description (component value like "10uF", "1kΩ")
                    description = value

                    if not part_number or not quantity:
                        continue

                    try:
                        quantity = int(float(quantity))  # Handle decimal quantities
                        if quantity <= 0:
                            continue
                    except (ValueError, TypeError):
                        continue

                    # Parse reference designators
                    reference_list = []
                    if references:
                        reference_list = [ref.strip() for ref in references.replace(',', ' ').split()]

                    # Try to find existing component
                    component = None
                    if part_number:
                        try:
                            component = Component.objects.get(part_number=part_number)
                        except Component.DoesNotExist:
                            pass

                    # Create BOM item
                    BoardBOMItem.objects.create(
                        board=board,
                        component=component,
                        part_number=part_number,
                        quantity_per_board=quantity,
                        value=value or '',
                        footprint=footprint or '',
                        description=description or '',
                        reference_list=reference_list  # Use reference_list instead of reference_designators
                    )

                except Exception as e:
                    print(f"Error parsing BOM row {row_num}: {e}")
                    continue

        except Exception as e:
            print(f"Error parsing BOM file: {e}")
            raise

    @action(detail=True, methods=['get'])
    def bom(self, request, pk=None):
        """Get BOM items for a board"""
        board = self.get_object()
        bom_items = board.bom_items.all().order_by('part_number')
        serializer = BoardBOMItemSerializer(bom_items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_component(self, request, pk=None):
        """Add a component to board BOM"""
        board = self.get_object()

        part_number = request.data.get('part_number')
        quantity_per_board = request.data.get('quantity_per_board', 1)
        value = request.data.get('value', '')
        footprint = request.data.get('footprint', '')
        description = request.data.get('description', '')
        reference_list = request.data.get('reference_list', [])

        if not part_number:
            return Response({'error': 'Part number is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            quantity_per_board = int(quantity_per_board)
            if quantity_per_board < 1:
                return Response({'error': 'Quantity must be at least 1'}, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({'error': 'Invalid quantity'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if component already exists in BOM
        if board.bom_items.filter(part_number=part_number).exists():
            return Response({'error': 'Component already exists in BOM'}, status=status.HTTP_400_BAD_REQUEST)

        # Try to find existing component in inventory
        component = None
        try:
            component = Component.objects.get(part_number=part_number)
        except Component.DoesNotExist:
            pass

        # Create BOM item
        bom_item = BoardBOMItem.objects.create(
            board=board,
            component=component,
            part_number=part_number,
            quantity_per_board=quantity_per_board,
            value=value,
            footprint=footprint,
            description=description,
            reference_list=reference_list
        )

        serializer = BoardBOMItemSerializer(bom_item)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def sync_inventory(self, request, pk=None):
        """Sync BOM components with inventory - create missing components with 0 stock"""
        board = self.get_object()

        created_components = []
        updated_components = []
        errors = []

        with transaction.atomic():
            for bom_item in board.bom_items.all():
                try:
                    # Skip if component is already linked
                    if bom_item.component:
                        continue

                    # Try to find existing component by part number
                    component = None
                    try:
                        component = Component.objects.get(part_number=bom_item.part_number)
                        # Link existing component to BOM item
                        bom_item.component = component
                        bom_item.save()
                        updated_components.append({
                            'part_number': component.part_number,
                            'name': component.name,
                            'action': 'linked'
                        })
                    except Component.DoesNotExist:
                        # Create new component with 0 stock
                        component = Component.objects.create(
                            part_number=bom_item.part_number,
                            name=bom_item.part_number,  # Use part number as name if no name provided
                            package=bom_item.value or '',  # Use value as package if available
                            footprint=bom_item.footprint or '',
                            description=bom_item.description or '',
                            stock_quantity=0,
                            min_stock=0,
                            created_by=request.user
                        )

                        # Link new component to BOM item
                        bom_item.component = component
                        bom_item.save()

                        created_components.append({
                            'part_number': component.part_number,
                            'name': component.name,
                            'action': 'created'
                        })

                except Exception as e:
                    errors.append({
                        'part_number': bom_item.part_number,
                        'error': str(e)
                    })

        return Response({
            'message': 'Inventory sync completed',
            'created': len(created_components),
            'updated': len(updated_components),
            'errors': len(errors),
            'created_components': created_components,
            'updated_components': updated_components,
            'error_details': errors
        })

    @action(detail=True, methods=['get'])
    def gerber(self, request, pk=None):
        """Get list of gerber files for a board"""
        board = self.get_object()

        if not board.gerber_path:
            return Response({'files': []})

        try:
            # List files in gerber directory
            gerber_files = []
            if default_storage.exists(board.gerber_path):
                dirs, files = default_storage.listdir(board.gerber_path)
                for file in files:
                    file_path = os.path.join(board.gerber_path, file)
                    gerber_files.append({
                        'name': file,
                        'url': default_storage.url(file_path),
                        'size': default_storage.size(file_path)
                    })

            return Response({'files': gerber_files})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], url_path='gerber-content', permission_classes=[])
    def gerber_content(self, request, pk=None):
        """Get content of a specific gerber file"""
        board = self.get_object()
        filename = request.GET.get('filename')

        if not board.gerber_path or not filename:
            return Response({'error': 'Invalid request'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construct file path
            file_path = os.path.join(board.gerber_path, filename)

            # Check if file exists
            if not default_storage.exists(file_path):
                return Response({'error': 'File not found'}, status=status.HTTP_404_NOT_FOUND)

            # Read file content
            with default_storage.open(file_path, 'r') as file:
                content = file.read()

            # Return content as plain text with CORS headers
            response = HttpResponse(content, content_type='text/plain')
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cache-Control, Pragma'
            return response

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'], url_path='gerber-zip', permission_classes=[])
    def gerber_zip(self, request, pk=None):
        """Get the entire gerber ZIP file by creating it from gerber directory"""
        board = self.get_object()

        if not board.gerber_path:
            return Response({'error': 'No gerber path found'}, status=status.HTTP_404_NOT_FOUND)

        try:
            import zipfile
            import io

            # Check if gerber directory exists
            if not default_storage.exists(board.gerber_path):
                return Response({'error': 'Gerber directory not found'}, status=status.HTTP_404_NOT_FOUND)

            # Create ZIP file in memory
            zip_buffer = io.BytesIO()

            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # List all files in gerber directory
                try:
                    dirs, files = default_storage.listdir(board.gerber_path)

                    for filename in files:
                        # Skip non-gerber files and macOS metadata
                        if filename.startswith('.') or '__MACOSX' in filename:
                            continue
                        if not filename.lower().endswith(('.gbr', '.gbl', '.gtl', '.gto', '.gbo', '.gts', '.gbs', '.gko', '.drl')):
                            continue

                        file_path = os.path.join(board.gerber_path, filename)

                        # Read file content and add to ZIP
                        with default_storage.open(file_path, 'rb') as file:
                            zip_file.writestr(filename, file.read())

                except Exception as list_error:
                    return Response({'error': f'Failed to list gerber files: {str(list_error)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Get ZIP content
            zip_content = zip_buffer.getvalue()
            zip_buffer.close()

            if len(zip_content) == 0:
                return Response({'error': 'No gerber files found in directory'}, status=status.HTTP_404_NOT_FOUND)

            # Return ZIP content with CORS headers
            response = HttpResponse(zip_content, content_type='application/zip')
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cache-Control, Pragma'
            response['Content-Disposition'] = f'attachment; filename="{board.name}_gerber.zip"'
            return response

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(xframe_options_exempt, name='dispatch')
    @action(detail=True, methods=['get'], url_path='ibom-viewer', permission_classes=[])
    def ibom_viewer(self, request, pk=None):
        """Serve iBOM HTML file without X-Frame-Options header and authentication"""
        board = self.get_object()

        if not board.ibom_file:
            return HttpResponse("iBOM file not found", status=404)

        try:
            # Read the iBOM HTML file
            with board.ibom_file.open('rb') as f:
                html_bytes = f.read()

            # Decode bytes to string with proper encoding handling
            try:
                html_content = html_bytes.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    html_content = html_bytes.decode('utf-8-sig')  # UTF-8 with BOM
                except UnicodeDecodeError:
                    html_content = html_bytes.decode('iso-8859-1')  # Fallback

            # Return HTML response without X-Frame-Options header
            response = HttpResponse(html_content, content_type='text/html; charset=utf-8')
            return response

        except Exception as e:
            return HttpResponse(f"Error loading iBOM file: {str(e)}", status=500)

    @action(detail=True, methods=['post'])
    def calculate(self, request, pk=None):
        """Calculate total components needed for given board quantity"""
        board = self.get_object()
        serializer = BoardCalculationSerializer(data=request.data)

        if serializer.is_valid():
            board_quantity = serializer.validated_data['board_quantity']

            calculations = []
            for bom_item in board.bom_items.all():
                required_quantity = bom_item.quantity_per_board * board_quantity
                available_quantity = bom_item.component.stock_quantity if bom_item.component else 0

                calculations.append({
                    'bom_item': BoardBOMItemSerializer(bom_item).data,
                    'required_quantity': required_quantity,
                    'available_quantity': available_quantity,
                    'shortage_quantity': max(0, required_quantity - available_quantity)
                })

            return Response({
                'board_quantity': board_quantity,
                'calculations': calculations
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def shortage(self, request, pk=None):
        """Get list of components that are in shortage for given board quantity"""
        board = self.get_object()
        board_quantity = int(request.query_params.get('quantity', 1))

        shortages = []
        for bom_item in board.bom_items.all():
            required_quantity = bom_item.quantity_per_board * board_quantity
            available_quantity = bom_item.component.stock_quantity if bom_item.component else 0
            shortage_quantity = required_quantity - available_quantity
            
            if shortage_quantity > 0:
                shortages.append({
                    'component': ComponentSerializer(bom_item.component).data if bom_item.component else None,
                    'bom_item': BoardBOMItemSerializer(bom_item).data,
                    'required_quantity': required_quantity,
                    'available_quantity': available_quantity,
                    'shortage_quantity': shortage_quantity
                })
        return Response({'shortages': shortages})


class InventoryTransactionViewSet(viewsets.ModelViewSet):
    """ViewSet for Inventory Transaction operations"""
    queryset = InventoryTransaction.objects.all()
    serializer_class = InventoryTransactionSerializer
    permission_classes = [IsSupervisor]

    def get_queryset(self):
        queryset = InventoryTransaction.objects.all()

        # Filter by component
        component = self.request.query_params.get('component', None)
        if component:
            queryset = queryset.filter(component__part_number=component)

        # Filter by transaction type
        transaction_type = self.request.query_params.get('type', None)
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)

        # Filter by board
        board = self.request.query_params.get('board', None)
        if board:
            queryset = queryset.filter(related_board__id=board)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class InventoryExportView(APIView):
    """Export inventory for board assembly"""
    permission_classes = [IsSupervisor]

    def post(self, request):
        board_id = request.data.get('board_id')
        board_quantity = request.data.get('board_quantity', 1)
        note = request.data.get('note', '')

        if not board_id:
            return Response({'error': 'Board ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            board = Board.objects.get(id=board_id)
        except Board.DoesNotExist:
            return Response({'error': 'Board not found'}, status=status.HTTP_404_NOT_FOUND)

        try:
            board_quantity = int(board_quantity)
            if board_quantity <= 0:
                raise ValueError()
        except (ValueError, TypeError):
            return Response({'error': 'Invalid board quantity'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if all components are available
        shortages = []
        transactions = []

        with transaction.atomic():
            for bom_item in board.bom_items.all():
                if not bom_item.component:
                    continue

                required_quantity = bom_item.quantity_per_board * board_quantity
                available_quantity = bom_item.component.stock_quantity

                if required_quantity > available_quantity:
                    shortages.append({
                        'component': bom_item.component.part_number,
                        'required': required_quantity,
                        'available': available_quantity,
                        'shortage': required_quantity - available_quantity
                    })
                else:
                    # Create inventory transaction
                    inventory_transaction = InventoryTransaction.objects.create(
                        component=bom_item.component,
                        quantity=-required_quantity,
                        transaction_type='out',
                        note=f"Export for board assembly: {board.name} (x{board_quantity}). {note}".strip(),
                        related_board=board,
                        board_quantity=board_quantity,
                        created_by=request.user
                    )
                    transactions.append(inventory_transaction)
                    # Update component stock quantity
                    bom_item.component.stock_quantity -= required_quantity
                    bom_item.component.save()

            if shortages:
                # Rollback transaction if there are shortages
                transaction.set_rollback(True)
                return Response({
                    'error': 'Insufficient stock for some components',
                    'shortages': shortages
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'message': f'Successfully exported inventory for {board_quantity} x {board.name}',
            'transactions': InventoryTransactionSerializer(transactions, many=True).data
        })

    def destroy(self, request, *args, **kwargs):
        """Delete board and associated files"""
        board = self.get_object()

        # Delete associated files
        if board.avatar:
            try:
                if os.path.exists(board.avatar.path):
                    os.remove(board.avatar.path)
            except:
                pass

        if board.bom_file:
            try:
                if os.path.exists(board.bom_file.path):
                    os.remove(board.bom_file.path)
            except:
                pass

        if board.ibom_file:
            try:
                if os.path.exists(board.ibom_file.path):
                    os.remove(board.ibom_file.path)
            except:
                pass

        if board.gerber_path and os.path.exists(board.gerber_path):
            try:
                import shutil
                shutil.rmtree(board.gerber_path)
            except:
                pass

        # Delete board and related BOM items (cascade delete)
        return super().destroy(request, *args, **kwargs)


class DashboardStatsView(APIView):
    """Get dashboard statistics"""
    permission_classes = [IsSupervisor]

    def get(self, request):
        stats = {
            'total_components': Component.objects.count(),
            'low_stock_components': Component.objects.filter(stock_quantity__lte=F('min_stock')).count(),
            'total_vendors': Vendor.objects.count(),
            'total_boards': Board.objects.count(),
            'recent_transactions': InventoryTransactionSerializer(
                InventoryTransaction.objects.all()[:10], many=True).data
        }

        # Recent low stock components
        low_stock_components = Component.objects.filter(
            stock_quantity__lte=F('min_stock')
        ).order_by('stock_quantity')[:5]

        stats['low_stock_list'] = ComponentListSerializer(low_stock_components, many=True).data

        return Response(stats)


class BoardViewSetExtended(BoardViewSet):
    """Extended BoardViewSet with additional Gerber rendering methods"""

    @action(detail=True, methods=['get'], url_path='gerber-svg', permission_classes=[])
    def gerber_svg(self, request, pk=None):
        """Render Gerber files to SVG on server side (fallback for browser compatibility issues)"""
        board = self.get_object()

        if not board.gerber_path:
            return Response({'error': 'No gerber path found'}, status=status.HTTP_404_NOT_FOUND)

        try:
            # Check if gerber directory exists
            if not default_storage.exists(board.gerber_path):
                return Response({'error': 'Gerber directory not found'}, status=status.HTTP_404_NOT_FOUND)

            # List gerber files
            try:
                dirs, files = default_storage.listdir(board.gerber_path)

                gerber_files = []
                for filename in files:
                    if filename.startswith('.') or '__MACOSX' in filename:
                        continue
                    if not filename.lower().endswith(('.gbr', '.gbl', '.gtl', '.gto', '.gbo', '.gts', '.gbs', '.gko', '.drl')):
                        continue

                    gerber_files.append({
                        'filename': filename,
                        'size': default_storage.size(os.path.join(board.gerber_path, filename)),
                    })

                if not gerber_files:
                    return Response({'error': 'No gerber files found'}, status=status.HTTP_404_NOT_FOUND)

                # Return file information for client-side processing
                response_data = {
                    'success': True,
                    'method': 'file-list',
                    'files': gerber_files,
                    'total_files': len(gerber_files),
                    'message': 'Use client-side rendering with proper polyfills',
                    'board_name': board.name,
                }

                # Add CORS headers
                response = Response(response_data)
                response['Access-Control-Allow-Origin'] = '*'
                response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
                response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Cache-Control, Pragma'
                return response

            except Exception as list_error:
                return Response({'error': f'Failed to process gerber files: {str(list_error)}'},
                              status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
