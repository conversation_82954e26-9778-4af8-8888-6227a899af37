import React, { useState, useEffect, useRef } from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Button,
    Box,
    Typography,
    Alert,
    CircularProgress,
    Card,
    CardContent,
    Divider,
    Chip,
    IconButton,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
    Edit as EditIcon,
    CloudUpload as UploadIcon,
    Image as ImageIcon,
    Archive as ZipIcon,
    Delete as DeleteIcon,
    PhotoCamera as CameraIcon,
} from "@mui/icons-material";
import { beeColors } from "../../Common/CustomButton";
import CustomButton from "../../Common/CustomButton";
import usePCBFlowStore from "../store/pcbFlowStore";

const EditBoardDialog = ({ open, onClose, board, onBoardUpdated }) => {
    const { updateBoard } = usePCBFlowStore();
    const [formData, setFormData] = useState({
        name: "",
        description: "",
        version: "",
        avatar: null,
        zipFile: null,
    });
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState("");
    const [avatarPreview, setAvatarPreview] = useState(null);
    const [zipFileName, setZipFileName] = useState("");
    const [dragOver, setDragOver] = useState({ avatar: false, zip: false });

    const avatarInputRef = useRef(null);
    const zipInputRef = useRef(null);

    // Initialize form data when board changes
    useEffect(() => {
        if (board) {
            setFormData({
                name: board.name || "",
                description: board.description || "",
                version: board.version || "",
                avatar: null,
                zipFile: null,
            });
            setAvatarPreview(board.avatar || null);
        }
    }, [board]);

    const handleInputChange = (field, value) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleAvatarChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setFormData((prev) => ({ ...prev, avatar: file }));
            // Create preview URL
            const previewUrl = URL.createObjectURL(file);
            setAvatarPreview(previewUrl);
        }
    };

    const handleZipFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setFormData((prev) => ({ ...prev, zipFile: file }));
            setZipFileName(file.name);
        }
    };

    // Drag & Drop handlers
    const handleDragOver = (e, type) => {
        e.preventDefault();
        setDragOver((prev) => ({ ...prev, [type]: true }));
    };

    const handleDragLeave = (e, type) => {
        e.preventDefault();
        setDragOver((prev) => ({ ...prev, [type]: false }));
    };

    const handleDrop = (e, type) => {
        e.preventDefault();
        setDragOver((prev) => ({ ...prev, [type]: false }));

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];

            if (type === "avatar" && file.type.startsWith("image/")) {
                setFormData((prev) => ({ ...prev, avatar: file }));
                const previewUrl = URL.createObjectURL(file);
                setAvatarPreview(previewUrl);
            } else if (type === "zip" && file.name.endsWith(".zip")) {
                setFormData((prev) => ({ ...prev, zipFile: file }));
                setZipFileName(file.name);
            }
        }
    };

    const handleRemoveAvatar = () => {
        setFormData((prev) => ({ ...prev, avatar: null }));
        setAvatarPreview(board?.avatar || null);
        if (avatarInputRef.current) {
            avatarInputRef.current.value = "";
        }
    };

    const handleRemoveZip = () => {
        setFormData((prev) => ({ ...prev, zipFile: null }));
        setZipFileName("");
        if (zipInputRef.current) {
            zipInputRef.current.value = "";
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!formData.name.trim()) {
            setError("Board name is required");
            return;
        }

        setSaving(true);
        setError("");

        try {
            const formDataToSend = new FormData();
            formDataToSend.append("name", formData.name);
            formDataToSend.append("description", formData.description);
            formDataToSend.append("version", formData.version);

            if (formData.avatar) {
                formDataToSend.append("avatar", formData.avatar);
            }

            if (formData.zipFile) {
                formDataToSend.append("file", formData.zipFile);
            }

            const updatedBoard = await updateBoard(board.id, formDataToSend);
            onBoardUpdated(updatedBoard);
            onClose();
        } catch (error) {
            console.error("Error updating board:", error);
            setError(error.response?.data?.error || "Failed to update board");
        } finally {
            setSaving(false);
        }
    };

    // Reset form when dialog closes
    useEffect(() => {
        if (!open) {
            setError("");
            setAvatarPreview(board?.avatar || null);
            setZipFileName("");
            setDragOver({ avatar: false, zip: false });
        }
    }, [open, board]);

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <EditIcon />
                    <Typography variant="h6">Edit Board Information</Typography>
                </Box>
            </DialogTitle>

            <form onSubmit={handleSubmit}>
                <DialogContent>
                    {error && (
                        <Alert severity="error" sx={{ mb: 2 }}>
                            {error}
                        </Alert>
                    )}

                    <Grid container spacing={3}>
                        <Grid size={{ xs: 12, md: 4 }}>
                            <Card>
                                <CardContent>
                                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                        Board Information
                                    </Typography>
                                    <TextField
                                        fullWidth
                                        label="Board Name"
                                        value={formData.name}
                                        onChange={(e) => handleInputChange("name", e.target.value)}
                                        required
                                        sx={{ mb: 2 }}
                                    />

                                    <TextField
                                        fullWidth
                                        label="Version"
                                        value={formData.version}
                                        onChange={(e) => handleInputChange("version", e.target.value)}
                                        sx={{ mb: 2 }}
                                    />

                                    <TextField
                                        fullWidth
                                        label="Description"
                                        value={formData.description}
                                        onChange={(e) => handleInputChange("description", e.target.value)}
                                        multiline
                                        rows={3}
                                        sx={{ mb: 2 }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid size={{ xs: 12, md: 8 }}>
                            {/* ZIP File Upload */}
                            <Card>
                                <CardContent>
                                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                        Update Board Files (Optional)
                                    </Typography>

                                    {/* Current ZIP file info */}
                                    {zipFileName && (
                                        <Box sx={{ mb: 2 }}>
                                            <Chip
                                                icon={<ZipIcon />}
                                                label={zipFileName}
                                                onDelete={handleRemoveZip}
                                                color="primary"
                                                variant="outlined"
                                            />
                                        </Box>
                                    )}

                                    {/* Drag & Drop Zone for ZIP */}
                                    <Box
                                        onDragOver={(e) => handleDragOver(e, "zip")}
                                        onDragLeave={(e) => handleDragLeave(e, "zip")}
                                        onDrop={(e) => handleDrop(e, "zip")}
                                        onClick={() => zipInputRef.current?.click()}
                                        sx={{
                                            border: `2px dashed ${dragOver.zip ? beeColors.primary.main : "#ccc"}`,
                                            borderRadius: 2,
                                            p: 4,
                                            textAlign: "center",
                                            cursor: "pointer",
                                            backgroundColor: dragOver.zip
                                                ? beeColors.primary.light + "20"
                                                : "transparent",
                                            transition: "all 0.3s ease",
                                            "&:hover": {
                                                borderColor: beeColors.primary.main,
                                                backgroundColor: beeColors.primary.light + "10",
                                            },
                                        }}
                                    >
                                        <ZipIcon sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
                                        <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                                            Drag & drop ZIP file here or click to browse
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                            Upload a new ZIP file to update BOM, Gerber, and iBOM files
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            Expected files: BOM.csv, iBOM.html, Gerber files, avatar.png
                                        </Typography>
                                    </Box>

                                    <input
                                        ref={zipInputRef}
                                        type="file"
                                        accept=".zip"
                                        onChange={handleZipFileChange}
                                        style={{ display: "none" }}
                                    />
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            {/* Avatar Upload */}
                            <Card>
                                <CardContent>
                                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                        Board Avatar
                                    </Typography>

                                    <Grid container spacing={2}>
                                        <Grid size={{ xs: 12, md: 6 }}>
                                            {/* Avatar Preview */}
                                            {avatarPreview && (
                                                <Box sx={{ mb: 2, position: "relative" }}>
                                                    <img
                                                        src={avatarPreview}
                                                        alt="Board avatar preview"
                                                        style={{
                                                            width: "100%",
                                                            maxHeight: 150,
                                                            objectFit: "contain",
                                                            backgroundColor: beeColors.background.main,
                                                            borderRadius: "8px",
                                                            border: "1px solid #e0e0e0",
                                                        }}
                                                    />
                                                    {formData.avatar && (
                                                        <IconButton
                                                            size="small"
                                                            sx={{
                                                                position: "absolute",
                                                                top: 4,
                                                                right: 4,
                                                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                                                "&:hover": {
                                                                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                                                                },
                                                            }}
                                                            onClick={handleRemoveAvatar}
                                                        >
                                                            <DeleteIcon fontSize="small" />
                                                        </IconButton>
                                                    )}
                                                </Box>
                                            )}
                                        </Grid>
                                        <Grid size={{ xs: 12, md: 6 }}>
                                            {/* Drag & Drop Zone */}
                                            <Box
                                                onDragOver={(e) => handleDragOver(e, "avatar")}
                                                onDragLeave={(e) => handleDragLeave(e, "avatar")}
                                                onDrop={(e) => handleDrop(e, "avatar")}
                                                onClick={() => avatarInputRef.current?.click()}
                                                sx={{
                                                    border: `2px dashed ${dragOver.avatar ? beeColors.primary.main : "#ccc"}`,
                                                    borderRadius: 2,
                                                    p: 3,
                                                    textAlign: "center",
                                                    cursor: "pointer",
                                                    backgroundColor: dragOver.avatar
                                                        ? beeColors.primary.light + "20"
                                                        : "transparent",
                                                    transition: "all 0.3s ease",
                                                    "&:hover": {
                                                        borderColor: beeColors.primary.main,
                                                        backgroundColor: beeColors.primary.light + "10",
                                                    },
                                                }}
                                            >
                                                <CameraIcon sx={{ fontSize: 48, color: "text.secondary", mb: 1 }} />
                                                <Typography variant="body2" color="text.secondary">
                                                    Drag & drop an image here or click to browse
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    Supports: JPG, PNG, GIF
                                                </Typography>
                                            </Box>

                                            <input
                                                ref={avatarInputRef}
                                                type="file"
                                                accept="image/*"
                                                onChange={handleAvatarChange}
                                                style={{ display: "none" }}
                                            />
                                        </Grid>
                                    </Grid>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </DialogContent>

                <DialogActions sx={{ p: 3 }}>
                    <Button onClick={onClose} disabled={saving}>
                        Cancel
                    </Button>
                    <CustomButton
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={saving}
                        startIcon={saving ? <CircularProgress size={20} /> : <EditIcon />}
                    >
                        {saving ? "Updating..." : "Update Board"}
                    </CustomButton>
                </DialogActions>
            </form>
        </Dialog>
    );
};

export default EditBoardDialog;
