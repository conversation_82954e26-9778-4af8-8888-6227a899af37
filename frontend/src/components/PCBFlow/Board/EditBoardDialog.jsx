import React, { useState, useEffect } from "react";
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Button,
    Box,
    Typography,
    Alert,
    CircularProgress,
    Grid,
    FormControl,
    InputLabel,
    Input,
    FormHelperText,
} from "@mui/material";
import { Edit as EditIcon, CloudUpload as UploadIcon } from "@mui/icons-material";
import { beeColors } from "../../Common/CustomButton";
import CustomButton from "../../Common/CustomButton";
import usePCBFlowStore from "../store/pcbFlowStore";

const EditBoardDialog = ({ open, onClose, board, onBoardUpdated }) => {
    const { updateBoard } = usePCBFlowStore();
    const [formData, setFormData] = useState({
        name: "",
        description: "",
        version: "",
        avatar: null,
        zipFile: null,
    });
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState("");
    const [avatarPreview, setAvatarPreview] = useState(null);

    // Initialize form data when board changes
    useEffect(() => {
        if (board) {
            setFormData({
                name: board.name || "",
                description: board.description || "",
                version: board.version || "",
                avatar: null,
                zipFile: null,
            });
            setAvatarPreview(board.avatar || null);
        }
    }, [board]);

    const handleInputChange = (field, value) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleAvatarChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setFormData((prev) => ({ ...prev, avatar: file }));
            // Create preview URL
            const previewUrl = URL.createObjectURL(file);
            setAvatarPreview(previewUrl);
        }
    };

    const handleZipFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setFormData((prev) => ({ ...prev, zipFile: file }));
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();

        if (!formData.name.trim()) {
            setError("Board name is required");
            return;
        }

        setSaving(true);
        setError("");

        try {
            const formDataToSend = new FormData();
            formDataToSend.append("name", formData.name);
            formDataToSend.append("description", formData.description);
            formDataToSend.append("version", formData.version);

            if (formData.avatar) {
                formDataToSend.append("avatar", formData.avatar);
            }

            if (formData.zipFile) {
                formDataToSend.append("file", formData.zipFile);
            }

            const updatedBoard = await updateBoard(board.id, formDataToSend);
            onBoardUpdated(updatedBoard);
            onClose();
        } catch (error) {
            console.error("Error updating board:", error);
            setError(error.response?.data?.error || "Failed to update board");
        } finally {
            setSaving(false);
        }
    };

    // Reset form when dialog closes
    useEffect(() => {
        if (!open) {
            setError("");
            setAvatarPreview(board?.avatar || null);
        }
    }, [open, board]);

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <EditIcon />
                    <Typography variant="h6">Edit Board Information</Typography>
                </Box>
            </DialogTitle>

            <form onSubmit={handleSubmit}>
                <DialogContent>
                    {error && (
                        <Alert severity="error" sx={{ mb: 2 }}>
                            {error}
                        </Alert>
                    )}

                    <Grid container spacing={3}>
                        <Grid item xs={12} md={8}>
                            <TextField
                                fullWidth
                                label="Board Name"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                required
                                sx={{ mb: 2 }}
                            />

                            <TextField
                                fullWidth
                                label="Version"
                                value={formData.version}
                                onChange={(e) => handleInputChange("version", e.target.value)}
                                sx={{ mb: 2 }}
                            />

                            <TextField
                                fullWidth
                                label="Description"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                                multiline
                                rows={3}
                                sx={{ mb: 2 }}
                            />
                        </Grid>

                        <Grid item xs={12} md={4}>
                            {/* Avatar Upload */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                    Board Avatar
                                </Typography>
                                {avatarPreview && (
                                    <Box sx={{ mb: 2 }}>
                                        <img
                                            src={avatarPreview}
                                            alt="Board avatar preview"
                                            style={{
                                                width: "100%",
                                                maxHeight: 150,
                                                objectFit: "contain",
                                                backgroundColor: beeColors.background.main,
                                                borderRadius: "8px",
                                                border: "1px solid #e0e0e0",
                                            }}
                                        />
                                    </Box>
                                )}
                                <FormControl fullWidth>
                                    <InputLabel shrink htmlFor="avatar-upload">
                                        Upload New Avatar
                                    </InputLabel>
                                    <Input
                                        id="avatar-upload"
                                        type="file"
                                        accept="image/*"
                                        onChange={handleAvatarChange}
                                        sx={{ mt: 2 }}
                                    />
                                    <FormHelperText>Upload a new image to replace the current avatar</FormHelperText>
                                </FormControl>
                            </Box>
                        </Grid>

                        <Grid item xs={12}>
                            {/* ZIP File Upload */}
                            <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                    Update Board Files (Optional)
                                </Typography>
                                <FormControl fullWidth>
                                    <InputLabel shrink htmlFor="zip-upload">
                                        Upload New ZIP File
                                    </InputLabel>
                                    <Input
                                        id="zip-upload"
                                        type="file"
                                        accept=".zip"
                                        onChange={handleZipFileChange}
                                        startAdornment={<UploadIcon sx={{ mr: 1, color: "text.secondary" }} />}
                                        sx={{ mt: 2 }}
                                    />
                                    <FormHelperText>
                                        Upload a new ZIP file to update BOM, Gerber, and iBOM files. Leave empty to keep
                                        current files.
                                    </FormHelperText>
                                </FormControl>
                            </Box>
                        </Grid>
                    </Grid>
                </DialogContent>

                <DialogActions sx={{ p: 3 }}>
                    <Button onClick={onClose} disabled={saving}>
                        Cancel
                    </Button>
                    <CustomButton
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={saving}
                        startIcon={saving ? <CircularProgress size={20} /> : <EditIcon />}
                    >
                        {saving ? "Updating..." : "Update Board"}
                    </CustomButton>
                </DialogActions>
            </form>
        </Dialog>
    );
};

export default EditBoardDialog;
