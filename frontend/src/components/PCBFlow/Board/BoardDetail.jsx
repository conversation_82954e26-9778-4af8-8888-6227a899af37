import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
    Box,
    Typography,
    Card,
    CardContent,
    Tabs,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    Button,
    TextField,
    IconButton,
    Tooltip,
    Alert,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    CircularProgress,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
    ArrowBack as ArrowBackIcon,
    Memory as BoardIcon,
    List as BOMIcon,
    Visibility as ViewIcon,
    // AccountTree as GerberIcon,
    Build as GerberIcon,
    ShoppingCart as PurchaseIcon,
    Calculate as CalculateIcon,
    Delete as DeleteIcon,
    MoreVert as MoreVertIcon,
    FileDownload as ExportIcon,
    Settings as ManageIcon,
    Add as AddIcon,
    Sync as SyncIcon,
    Output as ExportInventoryIcon,
} from "@mui/icons-material";
import { beeColors } from "../../Common/CustomButton";
import CustomButton from "../../Common/CustomButton";
import axiosInstance from "../../../services/axiosInstance";
import usePCBFlowStore from "../store/pcbFlowStore";
import GerberViewer from "./GerberViewer";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { convertBOMToExcelFormat, getBOMExcelColumnWidths } from "../../../utils/bomExcelUtils";
import {
    convertShortageToExcelFormat,
    getShortageExcelColumnWidths,
    createBoardInfoData,
    createVendorSummaryData,
    generateShortageFilename,
} from "../../../utils/shortageExcelUtils";
import VendorManagementDialog from "./VendorManagementDialog";
import AddComponentDialog from "./AddComponentDialog";

const BoardDetail = () => {
    const { boardId } = useParams();
    const navigate = useNavigate();
    const { vendors, fetchVendors } = usePCBFlowStore();
    const { deleteBoard } = usePCBFlowStore();

    const [board, setBoard] = useState(null);
    const [bomItems, setBomItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState(0);
    const [boardQuantity, setBoardQuantity] = useState(1);
    const [calculations, setCalculations] = useState([]);
    const [shortages, setShortages] = useState([]);
    const [deleteDialog, setDeleteDialog] = useState(false);
    const [deleting, setDeleting] = useState(false);
    const [calculating, setCalculating] = useState(false);
    const [checkingShortage, setCheckingShortage] = useState(false);
    const [vendorDialog, setVendorDialog] = useState({ open: false, component: null });
    const [addComponentDialog, setAddComponentDialog] = useState(false);
    const [syncing, setSyncing] = useState(false);
    const [exporting, setExporting] = useState(false);

    useEffect(() => {
        fetchBoardDetails();
        fetchBOM();
    }, [boardId]);

    const formatPrice = (price, currency) => {
        return new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: currency,
        }).format(price);
    };

    const fetchBoardDetails = async () => {
        try {
            const response = await axiosInstance.get(`/api/pcbflow/boards/${boardId}/`);
            setBoard(response.data);
        } catch (error) {
            console.error("Error fetching board details:", error);
        }
    };

    const fetchBOM = async () => {
        try {
            const response = await axiosInstance.get(`/api/pcbflow/boards/${boardId}/bom/`);
            setBomItems(response.data);
        } catch (error) {
            console.error("Error fetching BOM:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleCalculate = async () => {
        setCalculating(true);
        try {
            const responseShortage = await axiosInstance.get(
                `/api/pcbflow/boards/${boardId}/shortage/?quantity=${boardQuantity}`
            );
            setShortages(responseShortage.data.shortages);

            const responseCalculation = await axiosInstance.post(`/api/pcbflow/boards/${boardId}/calculate/`, {
                board_quantity: boardQuantity,
            });
            setCalculations(responseCalculation.data.calculations);
        } catch (error) {
            console.error("Error calculating requirements:", error);
        } finally {
            setCalculating(false);
        }
    };

    const handleCheckShortage = async () => {
        setCheckingShortage(true);
        try {
            const response = await axiosInstance.get(
                `/api/pcbflow/boards/${boardId}/shortage/?quantity=${boardQuantity}`
            );
            setShortages(response.data.shortages);
        } catch (error) {
            console.error("Error checking shortages:", error);
        } finally {
            setCheckingShortage(false);
        }
    };

    // Export BOM to Excel function
    const handleExportBOMToExcel = async () => {
        try {
            // Fetch vendors from store
            await fetchVendors();
            const { inventoryData, bomData, vendorData } = convertBOMToExcelFormat(bomItems, vendors, board);
            const { inventoryColumns, bomColumns, vendorColumns } = getBOMExcelColumnWidths();

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Create BOM sheet
            const bomWs = XLSX.utils.json_to_sheet(bomData);
            bomWs["!cols"] = bomColumns;
            XLSX.utils.book_append_sheet(wb, bomWs, "BOM");

            // Create Inventory sheet
            const inventoryWs = XLSX.utils.json_to_sheet(inventoryData);
            inventoryWs["!cols"] = inventoryColumns;
            XLSX.utils.book_append_sheet(wb, inventoryWs, "Inventory");

            // Create Vendor sheet
            const vendorWs = XLSX.utils.json_to_sheet(vendorData);
            vendorWs["!cols"] = vendorColumns;
            XLSX.utils.book_append_sheet(wb, vendorWs, "Vendor");

            // Generate filename with board name and timestamp
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
            const boardName = board?.name || "Board";
            const filename = `BOM_${boardName}_${timestamp}.xlsx`;

            // Save file
            const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
            const blob = new Blob([wbout], { type: "application/octet-stream" });
            saveAs(blob, filename);
        } catch (error) {
            console.error("Error exporting BOM to Excel:", error);
        }
    };

    // Export Shortage to Excel function
    const handleExportShortageToExcel = async () => {
        try {
            if (shortages.length === 0) {
                console.warn("No shortage data to export");
                return;
            }

            const { vendorSheets, boardInfo } = convertShortageToExcelFormat(shortages, board, boardQuantity);
            const { shortageColumns, infoColumns, summaryColumns } = getShortageExcelColumnWidths();

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Create Vendor Summary sheet (first sheet)
            const vendorSummaryData = createVendorSummaryData(vendorSheets, shortages);
            const summaryWs = XLSX.utils.json_to_sheet(vendorSummaryData);
            summaryWs["!cols"] = summaryColumns;
            XLSX.utils.book_append_sheet(wb, summaryWs, "Vendor Summary");

            // Create Board Info sheet
            const boardInfoData = createBoardInfoData(boardInfo);
            const infoWs = XLSX.utils.json_to_sheet(boardInfoData);
            infoWs["!cols"] = infoColumns;
            XLSX.utils.book_append_sheet(wb, infoWs, "Board Info");

            // Create individual vendor sheets
            Object.keys(vendorSheets).forEach((vendorName) => {
                const vendorData = vendorSheets[vendorName];
                const vendorWs = XLSX.utils.json_to_sheet(vendorData);
                vendorWs["!cols"] = shortageColumns;

                // Sanitize sheet name (Excel sheet names have restrictions)
                const sanitizedVendorName = vendorName
                    .replace(/[\\\/\?\*\[\]]/g, "_") // Replace invalid characters
                    .substring(0, 31); // Excel sheet name max length is 31 characters

                XLSX.utils.book_append_sheet(wb, vendorWs, sanitizedVendorName);
            });

            // Generate filename
            const filename = generateShortageFilename(board, boardQuantity);

            // Save file
            const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
            const blob = new Blob([wbout], { type: "application/octet-stream" });
            saveAs(blob, filename);
        } catch (error) {
            console.error("Error exporting shortage to Excel:", error);
        }
    };

    const handleOpenVendorDialog = (component) => {
        setVendorDialog({ open: true, component });
    };

    const handleCloseVendorDialog = () => {
        setVendorDialog({ open: false, component: null });
    };

    const handleVendorUpdated = async () => {
        // Refresh shortage data to get updated vendor information
        if (shortages.length > 0) {
            await handleCheckShortage();
        }
    };

    const handleDeleteBoard = async () => {
        setDeleting(true);
        try {
            await deleteBoard(boardId);
            navigate("/pcbflow/boards");
        } catch (error) {
            console.error("Error deleting board:", error);
        } finally {
            setDeleting(false);
            setDeleteDialog(false);
        }
    };

    const handleSyncInventory = async () => {
        setSyncing(true);
        try {
            const response = await axiosInstance.post(`/api/pcbflow/boards/${boardId}/sync_inventory/`);
            const results = response.data;

            alert(
                `Inventory sync completed!\n` +
                    `Created: ${results.created} components\n` +
                    `Linked: ${results.updated} components\n` +
                    `Errors: ${results.errors}`
            );

            // Refresh BOM to show updated component links
            await fetchBOM();
        } catch (error) {
            console.error("Error syncing inventory:", error);
            alert("Failed to sync inventory");
        } finally {
            setSyncing(false);
        }
    };

    const handleComponentAdded = async () => {
        // Refresh BOM and board details after adding component
        await fetchBOM();
        await fetchBoardDetails();
    };

    const handleExportInventory = async () => {
        if (
            window.confirm(
                "[" + board.name + "] Are you sure you want to export inventory for " + boardQuantity + " board(s)?"
            ) === false
        ) {
            return;
        }
        setExporting(true);
        try {
            const response = await axiosInstance.post("/api/pcbflow/inventory/export/", {
                board_id: boardId,
                board_quantity: boardQuantity,
                note: `Exported from Board Detail page`,
            });

            alert(
                `Inventory exported successfully!\n` +
                    `${response.data.message}\n` +
                    `${response.data.transactions.length} transactions created.`
            );

            // Refresh calculations and shortage data to show updated stock
            await handleCalculate();
        } catch (error) {
            console.error("Error exporting inventory:", error);
            if (error.response?.data?.shortages) {
                const shortageList = error.response.data.shortages
                    .map((s) => `${s.component}: need ${s.required}, have ${s.available}`)
                    .join("\n");
                alert(`Export failed due to shortages:\n${shortageList}`);
            } else {
                alert("Failed to export inventory");
            }
        } finally {
            setExporting(false);
        }
    };

    const TabPanel = ({ children, value, index }) => (
        <div hidden={value !== index}>{value === index && <Box sx={{ py: 3 }}>{children}</Box>}</div>
    );

    if (loading) {
        return (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: 400 }}>
                <Typography>Loading board details...</Typography>
            </Box>
        );
    }

    if (!board) {
        return (
            <Box sx={{ textAlign: "center", py: 8 }}>
                <Typography variant="h6" color="text.secondary">
                    Board not found
                </Typography>
                <Button onClick={() => navigate("/pcbflow/boards")} sx={{ mt: 2 }}>
                    Back to Boards
                </Button>
            </Box>
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <IconButton onClick={() => navigate("/pcbflow/boards")} sx={{ mr: 2 }}>
                    <ArrowBackIcon />
                </IconButton>
                <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h4" fontWeight="bold" color="text.primary">
                        {board.name}
                    </Typography>
                </Box>
                <IconButton onClick={() => setDeleteDialog(true)}>
                    <DeleteIcon />
                </IconButton>
            </Box>

            {/* Board Info Card */}
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Grid container spacing={3}>
                        <Grid size={{ xs: 12, md: 3 }}>
                            {board.avatar ? (
                                <img
                                    src={board.avatar}
                                    alt={board.name}
                                    style={{
                                        width: "100%",
                                        height: 200,
                                        objectFit: "contain",
                                        backgroundColor: beeColors.background.main,
                                        borderRadius: "20px",
                                    }}
                                />
                            ) : (
                                <Box
                                    sx={{
                                        width: "100%",
                                        height: 200,
                                        backgroundColor: beeColors.background.main,
                                        borderRadius: 2,
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                >
                                    <BoardIcon sx={{ fontSize: 64, color: "text.secondary" }} />
                                </Box>
                            )}
                        </Grid>
                        <Grid size={{ xs: 12, md: 9 }}>
                            {board.description && (
                                <Typography variant="h6" fontWeight="bold" color="text.primary">
                                    {board.name}
                                </Typography>
                            )}
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 6, md: 3 }}>
                                    <Typography variant="h4" fontWeight="bold" color={beeColors.primary.main}>
                                        {board.total_components || 0}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Unique Components
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }}>
                                    <Typography variant="h4" fontWeight="bold" color={beeColors.secondary.main}>
                                        {board.total_parts_per_board || 0}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Total Parts
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Uploaded by
                                    </Typography>
                                    <Typography variant="subtitle2" fontWeight="bold">
                                        {board.uploaded_by?.username || "Unknown"}
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Upload Date
                                    </Typography>
                                    <Typography variant="subtitle2" fontWeight="bold">
                                        {new Date(board.created_at).toLocaleDateString()}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* Tabs */}
            <Card>
                <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                    <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
                        <Tab icon={<BOMIcon />} label="BOM" />
                        <Tab icon={<ViewIcon />} label="iBOM" />
                        <Tab icon={<GerberIcon />} label="Gerber" />
                        <Tab icon={<PurchaseIcon />} label="Purchase" />
                    </Tabs>
                </Box>

                {/* BOM Tab */}
                <TabPanel value={activeTab} index={0}>
                    {/* Header with buttons */}
                    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", p: 2 }}>
                        <Typography variant="h6" fontWeight="bold">
                            Bill of Materials ({bomItems.length} items)
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1 }}>
                            <CustomButton
                                variant="outlined"
                                startIcon={<AddIcon />}
                                onClick={() => setAddComponentDialog(true)}
                                sx={{
                                    borderColor: beeColors.secondary.main,
                                    color: beeColors.secondary.main,
                                    "&:hover": {
                                        backgroundColor: beeColors.secondary.light,
                                        borderColor: beeColors.secondary.main,
                                    },
                                }}
                            >
                                Add Component
                            </CustomButton>
                            <CustomButton
                                variant="outlined"
                                startIcon={syncing ? <CircularProgress size={20} /> : <SyncIcon />}
                                onClick={handleSyncInventory}
                                disabled={syncing}
                                sx={{
                                    borderColor: beeColors.accent.main,
                                    color: beeColors.accent.main,
                                    "&:hover": {
                                        backgroundColor: beeColors.accent.light,
                                        borderColor: beeColors.accent.main,
                                    },
                                }}
                            >
                                {syncing ? "Syncing..." : "Sync Inventory"}
                            </CustomButton>
                            <CustomButton
                                variant="outlined"
                                startIcon={<ExportIcon />}
                                onClick={handleExportBOMToExcel}
                                sx={{
                                    borderColor: beeColors.primary.main,
                                    color: beeColors.primary.main,
                                    "&:hover": {
                                        backgroundColor: beeColors.primary.light,
                                        borderColor: beeColors.primary.main,
                                    },
                                }}
                            >
                                Export BOM to Excel
                            </CustomButton>
                        </Box>
                    </Box>
                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow sx={{ backgroundColor: beeColors.background.main }}>
                                    <TableCell>#</TableCell>
                                    <TableCell>Part Number</TableCell>
                                    <TableCell>Value</TableCell>
                                    <TableCell>Footprint</TableCell>
                                    <TableCell align="center">Quantity</TableCell>
                                    <TableCell>References</TableCell>
                                    <TableCell align="center">Stock</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {bomItems.map((item, index) => (
                                    <TableRow key={index}>
                                        <TableCell>{index + 1}</TableCell>
                                        <TableCell>
                                            <Typography variant="subtitle2" fontWeight="bold">
                                                {item.part_number}
                                            </Typography>
                                            {item.component && (
                                                <Typography variant="caption" color="text.secondary">
                                                    {item.component.name}
                                                </Typography>
                                            )}
                                        </TableCell>
                                        <TableCell>{item.value || "-"}</TableCell>
                                        <TableCell>
                                            <Chip label={item.footprint || "N/A"} size="small" variant="outlined" />
                                        </TableCell>
                                        <TableCell align="center">
                                            <Typography variant="h6" fontWeight="bold">
                                                {item.quantity_per_board}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Typography variant="body2" sx={{ maxWidth: 200 }}>
                                                {item.reference_designators || "-"}
                                            </Typography>
                                        </TableCell>
                                        <TableCell align="center">
                                            {item.component ? (
                                                <Chip
                                                    label={item.component.stock_quantity}
                                                    color={item.component.is_low_stock ? "warning" : "success"}
                                                    size="small"
                                                />
                                            ) : (
                                                <Chip label="Not Mapped" color="error" size="small" />
                                            )}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </TabPanel>

                {/* iBOM Tab */}
                <TabPanel value={activeTab} index={1}>
                    {board.ibom_file ? (
                        <Box sx={{ height: 800, border: 1, borderColor: "divider", borderRadius: 1 }}>
                            <iframe
                                src={`${import.meta.env.VITE_API_URL}/api/pcbflow/boards/${boardId}/ibom-viewer/`}
                                width="100%"
                                height="100%"
                                style={{ border: "none" }}
                                title="Interactive BOM"
                            />
                        </Box>
                    ) : (
                        <Alert severity="info">No iBOM file available for this board.</Alert>
                    )}
                </TabPanel>

                {/* Gerber Tab */}
                <TabPanel value={activeTab} index={2}>
                    <GerberViewer boardId={boardId} gerberPath={board.gerber_path} />
                </TabPanel>

                {/* Purchase Tab */}
                <TabPanel value={activeTab} index={3}>
                    <Grid container spacing={3} sx={{ p: 4 }}>
                        <Grid size={{ xs: 12, md: 4 }}>
                            <Card sx={{ p: 2, backgroundColor: beeColors.background.main }}>
                                <Typography variant="h6" sx={{ mb: 2 }}>
                                    Calculate Requirements
                                </Typography>
                                <Box sx={{ display: "flex", gap: 1 }}>
                                    <TextField
                                        fullWidth
                                        size="small"
                                        label="Board Quantity"
                                        type="number"
                                        value={boardQuantity}
                                        onChange={(e) => setBoardQuantity(parseInt(e.target.value) || 1)}
                                        inputProps={{ min: 1 }}
                                        sx={{ mb: 2 }}
                                    />
                                    <Box sx={{ display: "flex", gap: 1 }}>
                                        <CustomButton
                                            variant="contained"
                                            color="primary"
                                            onClick={handleCalculate}
                                            startIcon={<CalculateIcon />}
                                            size="small"
                                            sx={{ height: "40px" }}
                                            disabled={calculating}
                                        >
                                            {calculating ? "Calculating..." : "Calculate"}
                                        </CustomButton>
                                    </Box>
                                </Box>
                            </Card>
                        </Grid>
                        <Grid size={{ xs: 12, md: 8 }}>
                            {shortages.length > 0 && (
                                <Alert severity="warning" sx={{ mb: 2 }}>
                                    {shortages.length} component(s) are in shortage for {boardQuantity} board(s).
                                </Alert>
                            )}
                            {calculations.length > 0 && shortages.length === 0 && (
                                <Alert severity="success" sx={{ mb: 2 }}>
                                    All components are available for {boardQuantity} board(s). No shortages detected!
                                </Alert>
                            )}
                            {calculations.length === 0 && shortages.length === 0 && (
                                <Alert severity="info" sx={{ mb: 2 }}>
                                    Enter the board quantity and click "Calculate" to see component requirements, or
                                    "Check Shortage" to identify missing components.
                                </Alert>
                            )}
                        </Grid>
                    </Grid>

                    {/* Shortages Results Table */}
                    {shortages.length > 0 && (
                        <Box sx={{ p: 4 }}>
                            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                                <Typography variant="h6">Components in Shortage ({shortages.length} items)</Typography>
                                <CustomButton
                                    variant="contained"
                                    color="error"
                                    startIcon={<ExportIcon />}
                                    onClick={handleExportShortageToExcel}
                                    size="small"
                                >
                                    Export Shortage List
                                </CustomButton>
                            </Box>
                            <TableContainer component={Paper}>
                                <Table>
                                    <TableHead>
                                        <TableRow sx={{ backgroundColor: beeColors.background.main }}>
                                            <TableCell>#</TableCell>
                                            <TableCell>Part Number</TableCell>
                                            <TableCell align="center">Required</TableCell>
                                            <TableCell align="center">Available</TableCell>
                                            <TableCell align="center">Shortage</TableCell>
                                            <TableCell>Preferred Vendor</TableCell>
                                            <TableCell align="center">Price</TableCell>
                                            <TableCell>Link</TableCell>
                                            <TableCell align="center">Manage</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {shortages.map((shortage, index) => {
                                            const component = shortage.component;
                                            const bomItem = shortage.bom_item;
                                            const preferredVendor = component?.preferred_vendor;

                                            return (
                                                <TableRow key={index}>
                                                    <TableCell>{index + 1}</TableCell>
                                                    <TableCell>
                                                        <Typography variant="subtitle2" fontWeight="bold">
                                                            {bomItem?.part_number || "-"}
                                                        </Typography>
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <Typography variant="h6" fontWeight="bold" color="primary">
                                                            {shortage.required_quantity}
                                                        </Typography>
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <Typography variant="body2">
                                                            {shortage.available_quantity}
                                                        </Typography>
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <Chip
                                                            label={shortage.shortage_quantity}
                                                            color="error"
                                                            size="small"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        {preferredVendor ? (
                                                            <Typography variant="body2" fontWeight="bold">
                                                                {preferredVendor.vendor?.name || "-"}
                                                            </Typography>
                                                        ) : (
                                                            <Chip label="No Vendor" color="warning" size="small" />
                                                        )}
                                                    </TableCell>
                                                    <TableCell align="right">
                                                        {preferredVendor ? (
                                                            <Typography variant="body2" color="success.main">
                                                                {formatPrice(
                                                                    preferredVendor.price,
                                                                    preferredVendor.currency
                                                                )}
                                                            </Typography>
                                                        ) : (
                                                            <Typography variant="body2" color="text.secondary">
                                                                -
                                                            </Typography>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        {preferredVendor?.link ? (
                                                            <Tooltip title="Open vendor link">
                                                                <IconButton
                                                                    size="small"
                                                                    onClick={() =>
                                                                        window.open(preferredVendor.link, "_blank")
                                                                    }
                                                                >
                                                                    <ViewIcon />
                                                                </IconButton>
                                                            </Tooltip>
                                                        ) : (
                                                            <Typography variant="body2" color="text.secondary">
                                                                -
                                                            </Typography>
                                                        )}
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <CustomButton
                                                            variant="outlined"
                                                            color="primary"
                                                            size="small"
                                                            startIcon={<ManageIcon />}
                                                            onClick={() => handleOpenVendorDialog(component)}
                                                        >
                                                            Manage
                                                        </CustomButton>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Box>
                    )}

                    {/* Calculations Results Table */}
                    {calculations.length > 0 && (
                        <>
                            <Divider sx={{ mb: 4 }} />
                            <Box sx={{ p: 4 }}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        mb: 2,
                                    }}
                                >
                                    <Typography variant="h6" sx={{ mb: 2 }}>
                                        Component Requirements for {boardQuantity} Board(s)
                                    </Typography>
                                    {calculations.length > 0 && shortages.length === 0 && (
                                        <Box sx={{ display: "flex", justifyContent: "center", mb: 2 }}>
                                            <CustomButton
                                                variant="contained"
                                                color="success"
                                                startIcon={
                                                    exporting ? <CircularProgress size={20} /> : <ExportInventoryIcon />
                                                }
                                                onClick={handleExportInventory}
                                                disabled={exporting}
                                                size="large"
                                                sx={{
                                                    backgroundColor: beeColors.success.main,
                                                    "&:hover": {
                                                        backgroundColor: beeColors.success.dark,
                                                    },
                                                }}
                                            >
                                                {exporting
                                                    ? "Exporting..."
                                                    : `Export Inventory for ${boardQuantity} Board(s)`}
                                            </CustomButton>
                                        </Box>
                                    )}
                                </Box>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableHead>
                                            <TableRow sx={{ backgroundColor: beeColors.background.main }}>
                                                <TableCell>#</TableCell>
                                                <TableCell>Part Number</TableCell>
                                                <TableCell align="center">Qty per Board</TableCell>
                                                <TableCell align="center">Required Qty</TableCell>
                                                <TableCell align="center">Available Stock</TableCell>
                                                <TableCell align="center">Status</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {calculations.map((calc, index) => {
                                                const isShortage = calc.shortage_quantity > 0;
                                                return (
                                                    <TableRow key={index}>
                                                        <TableCell>{index + 1}</TableCell>
                                                        <TableCell>
                                                            <Typography variant="subtitle2" fontWeight="bold">
                                                                {calc.bom_item.part_number}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            <Typography variant="body2">
                                                                {calc.bom_item.quantity_per_board}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            <Typography variant="h6" fontWeight="bold">
                                                                {calc.required_quantity}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            <Typography variant="body2">
                                                                {calc.available_quantity}
                                                            </Typography>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            <Chip
                                                                label={
                                                                    isShortage
                                                                        ? `Short: ${calc.shortage_quantity}`
                                                                        : "OK"
                                                                }
                                                                color={isShortage ? "error" : "success"}
                                                                size="small"
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            })}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </Box>
                        </>
                    )}
                </TabPanel>
            </Card>

            {/* Delete Confirmation Dialog */}
            <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
                <DialogTitle>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                        <DeleteIcon sx={{ color: beeColors.error.main }} />
                        <Typography variant="h6" fontWeight="bold">
                            Delete Board
                        </Typography>
                    </Box>
                </DialogTitle>

                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        Are you sure you want to delete the board "{board?.name}"?
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        This action will permanently delete all board data and files.
                    </Typography>
                    <Typography variant="body2" color="error" sx={{ mt: 2, fontWeight: "bold" }}>
                        This action cannot be undone.
                    </Typography>
                </DialogContent>

                <DialogActions sx={{ p: 3 }}>
                    <Button onClick={() => setDeleteDialog(false)} disabled={deleting}>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleDeleteBoard}
                        color="error"
                        variant="contained"
                        disabled={deleting}
                        startIcon={<DeleteIcon />}
                    >
                        {deleting ? "Deleting..." : "Delete Board"}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Vendor Management Dialog */}
            <VendorManagementDialog
                open={vendorDialog.open}
                onClose={handleCloseVendorDialog}
                component={vendorDialog.component}
                onVendorUpdated={handleVendorUpdated}
            />

            {/* Add Component Dialog */}
            <AddComponentDialog
                open={addComponentDialog}
                onClose={() => setAddComponentDialog(false)}
                boardId={boardId}
                onComponentAdded={handleComponentAdded}
            />
        </Box>
    );
};

export default BoardDetail;
